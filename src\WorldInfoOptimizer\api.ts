/**
 * 世界书优化器 API 交互模块
 * 包含所有与 SillyTavern API 交互的函数
 */

import { MESSAGES, PANEL_ID } from './constants';
import {
  appState,
  safeClearLorebookEntries,
  safeSetLorebookEntries,
  setDataLoaded,
  updateCharacterRegexes,
} from './state';
import type { TavernHelperAPI } from './types';
import { errorCatched } from './utils';

// --- 全局变量 ---
let TavernHelper: TavernHelperAPI;
let parentWin: any;
let $: any;

// --- API 包装器 ---
export let TavernAPI: any = null;

/**
 * 设置全局依赖
 */
export const setGlobalDependencies = (jquery: any, parentWindow: any, tavernHelper: TavernHelperAPI) => {
  $ = jquery;
  parentWin = parentWindow;
  TavernHelper = tavernHelper;
};

/**
 * 初始化 TavernAPI
 */
export const initializeTavernAPI = () => {
  TavernAPI = {
    createLorebook: errorCatched(async (name: string) => await TavernHelper.createLorebook(name), 'createLorebook'),
    deleteLorebook: errorCatched(async (name: string) => await TavernHelper.deleteLorebook(name), 'deleteLorebook'),
    getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks(), 'getLorebooks'),
    setLorebookSettings: errorCatched(
      async (settings: any) => await TavernHelper.setLorebookSettings(settings),
      'setLorebookSettings',
    ),
    getCharData: errorCatched(async () => await TavernHelper.getCharData(), 'getCharData'),
    Character: TavernHelper.Character || null,
    getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' }), 'getRegexes'),
    replaceRegexes: errorCatched(
      async (regexes: any) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' }),
      'replaceRegexes',
    ),
    createLorebookEntries: errorCatched(
      async (bookName: string, entries: any[]) => await TavernHelper.createLorebookEntries(bookName, entries),
      'createLorebookEntries',
    ),
    setLorebookEntries: errorCatched(
      async (bookName: string, entries: any[]) => await TavernHelper.setLorebookEntries(bookName, entries),
      'setLorebookEntries',
    ),
    deleteLorebookEntry: errorCatched(
      async (bookName: string, uid: number) => await TavernHelper.deleteLorebookEntry(bookName, uid),
      'deleteLorebookEntry',
    ),
    getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings(), 'getLorebookSettings'),
    getCurrentCharLorebooks: errorCatched(
      async () => await TavernHelper.getCurrentCharLorebooks(),
      'getCurrentCharLorebooks',
    ),
    getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook(), 'getChatLorebook'),
  };
};

/**
 * 更新进度显示
 */
const updateProgress = (percentage: number, text: string) => {
  const $progressBar = $('#wio-loading-bar', parentWin.document);
  const $statusText = $('#wio-loading-status', parentWin.document);

  if ($progressBar.length) $progressBar.css('width', `${Math.min(100, Math.max(0, percentage))}%`);
  if ($statusText.length) $statusText.text(text);
};

/**
 * 加载所有数据
 */
export const loadAllData = errorCatched(async (renderContent: () => void) => {
  const $content = $(`#${PANEL_ID}-content`, parentWin.document);
  $content.html(`
    <div class="wio-loading-container">
      <div class="wio-loading-title">数据同步中...</div>
      <div class="wio-loading-progress-bar-container">
        <div id="wio-loading-bar" class="wio-loading-progress-bar" style="width: 0%;"></div>
      </div>
      <div id="wio-loading-status" class="wio-loading-status-text">${MESSAGES.LOADING.INITIALIZING}</div>
    </div>
  `);

  try {
    updateProgress(5, MESSAGES.LOADING.CONNECTING_API);

    // 防御性检查：确保SillyTavern API可用
    if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {
      console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');
      appState.regexes.global = [];
      appState.regexes.character = [];
      appState.allLorebooks = [];
      appState.lorebooks.character = [];
      appState.chatLorebook = null;
      safeClearLorebookEntries();
      setDataLoaded(true);
      renderContent();
      return;
    }

    const context = parentWin.SillyTavern.getContext() || {};
    const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
    const hasActiveChat = context.chatId !== undefined && context.chatId !== null;

    let charData: any = null,
      charLinkedBooks: any = null,
      chatLorebook: any = null;

    updateProgress(10, MESSAGES.LOADING.FETCHING_CORE_DATA);

    // 使用Promise.allSettled来避免单个失败影响整体
    const promises = [
      TavernAPI.getRegexes().catch(() => []),
      TavernAPI.getLorebookSettings().catch(() => ({})),
      TavernAPI.getLorebooks().catch(() => []),
    ];

    if (hasActiveCharacter) {
      promises.push(TavernAPI.getCharData().catch(() => null));
      promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));
    } else {
      promises.push(Promise.resolve(null), Promise.resolve(null));
    }

    if (hasActiveChat) {
      promises.push(
        TavernAPI.getChatLorebook().catch((error: any) => {
          console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);
          return null;
        }),
      );
    } else {
      promises.push(Promise.resolve(null));
    }

    const results = await Promise.allSettled(promises);
    updateProgress(20, MESSAGES.LOADING.ANALYZING_DATA);

    // 安全提取结果
    const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
    const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
    const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];
    charData = results[3]?.status === 'fulfilled' ? results[3].value : null;
    charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;
    chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;

    appState.regexes.global = Array.isArray(allUIRegexes) ? allUIRegexes.filter((r: any) => r.scope === 'global') : [];
    updateCharacterRegexes(allUIRegexes, charData);

    // 处理世界书数据
    updateProgress(30, MESSAGES.LOADING.LOADING_LOREBOOKS);

    appState.allLorebooks = Array.isArray(allBookFileNames)
      ? allBookFileNames.map((name: string) => ({
          name,
          enabled: globalSettings.world_info_include?.includes(name) || false,
        }))
      : [];

    appState.lorebooks.character = Array.isArray(charLinkedBooks) ? charLinkedBooks : [];
    appState.chatLorebook = chatLorebook;

    // 加载世界书条目
    updateProgress(50, MESSAGES.LOADING.PROCESSING_ENTRIES);
    await loadLorebookEntries(allBookFileNames);

    updateProgress(90, MESSAGES.LOADING.FINALIZING);
    setDataLoaded(true);

    updateProgress(100, MESSAGES.LOADING.COMPLETE);
    setTimeout(() => {
      renderContent();
    }, 500);
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error loading data:', error);
    updateProgress(100, '数据加载失败，请尝试刷新');
    setTimeout(() => {
      $content.html('<p class="wio-error-text">数据加载失败，请点击刷新按钮重试。</p>');
    }, 1000);
  }
}, 'loadAllData');

/**
 * 加载世界书条目
 */
const loadLorebookEntries = async (bookNames: string[]) => {
  safeClearLorebookEntries();

  if (!Array.isArray(bookNames) || bookNames.length === 0) {
    console.log('[WorldInfoOptimizer] No lorebooks to load entries for');
    return;
  }

  const batchSize = 5;
  const totalBooks = bookNames.length;
  let processedBooks = 0;

  for (let i = 0; i < bookNames.length; i += batchSize) {
    const batch = bookNames.slice(i, i + batchSize);
    const batchPromises = batch.map(async (bookName: string) => {
      try {
        const entries = await TavernHelper.getLorebookEntries(bookName);
        if (Array.isArray(entries)) {
          safeSetLorebookEntries(bookName, entries);
        }
        processedBooks++;
        const progress = 50 + (processedBooks / totalBooks) * 30;
        updateProgress(progress, `${MESSAGES.LOADING.PROCESSING_ENTRIES} (${processedBooks}/${totalBooks})`);
      } catch (error) {
        console.warn(`[WorldInfoOptimizer] Failed to load entries for ${bookName}:`, error);
        processedBooks++;
      }
    });

    await Promise.allSettled(batchPromises);

    // 小延迟避免过度请求
    if (i + batchSize < bookNames.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`[WorldInfoOptimizer] Loaded entries for ${processedBooks} lorebooks`);
};

/**
 * 刷新数据
 */
export const refreshData = async (renderContent: () => void) => {
  console.log('[WorldInfoOptimizer] Refreshing data...');
  setDataLoaded(false);
  await loadAllData(renderContent);
};

/**
 * 创建世界书
 */
export const createLorebook = async (name: string): Promise<boolean> => {
  try {
    await TavernAPI.createLorebook(name);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to create lorebook:', error);
    return false;
  }
};

/**
 * 删除世界书
 */
export const deleteLorebook = async (name: string): Promise<boolean> => {
  try {
    await TavernAPI.deleteLorebook(name);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to delete lorebook:', error);
    return false;
  }
};

/**
 * 创建世界书条目
 */
export const createLorebookEntry = async (bookName: string, entryData: Partial<LorebookEntry>): Promise<boolean> => {
  try {
    await TavernAPI.createLorebookEntries(bookName, [entryData]);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to create lorebook entry:', error);
    return false;
  }
};

/**
 * 删除世界书条目
 */
export const deleteLorebookEntry = async (bookName: string, uid: number): Promise<boolean> => {
  try {
    await TavernAPI.deleteLorebookEntry(bookName, uid);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to delete lorebook entry:', error);
    return false;
  }
};

/**
 * 更新世界书条目
 */
export const updateLorebookEntries = async (bookName: string, entries: any[]): Promise<boolean> => {
  try {
    await TavernAPI.setLorebookEntries(bookName, entries);
    return true;
  } catch (error) {
    console.error('[WorldInfoOptimizer] Failed to update lorebook entries:', error);
    return false;
  }
};
