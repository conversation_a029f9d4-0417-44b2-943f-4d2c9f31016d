import{default as n}from'https://testingcf.jsdelivr.net/npm/dedent/+esm';var e={4:()=>{$(()=>{replaceScriptButtons([{name:'晚上好',visible:!0}]),eventOn(getButtonEvent('晚上好'),()=>{toastr.warning('晚安, 络络')})})},33:()=>{eventOn(tavern_events.MESSAGE_UPDATED,n=>{toastr.error(`谁让你动我第 ${n} 楼消息的😡`,'干什么!')})},478:()=>{$(()=>{toastr.success('你已经成功加载示例脚本!','恭喜你!')}),$(window).on('pagehide',()=>{toastr.info('你已经卸载示例脚本!','再见!')})}},t={};function s(n){var r=t[n];if(void 0!==r)return r.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,s),a.exports}s(478),s(4),s(33);$(async()=>{0===getLastMessageId()&&await createChatMessages([{role:'assistant',message:n('\n                   [查看日记: 这是第二次看我的日记了呢~]\n                   <roleplay_options>\n                   接受日记本并翻阅: 青空黎接过她递来的粉色日记本，在天台阳光下缓缓翻开第一页\n                   保持沉默盯着她看: 青空黎没有接本子，只是盯着她略显紧张的表情和轻颤的声音\n                   坐到她身边: 青空黎没有立刻回应，而是缓缓走到络络身边坐下，等待她自己继续说\n                   开玩笑化解气氛: 青空黎微微一笑，开玩笑地说「所以，是要让我当监督官啦？」\n                   跳过时间: 青空黎接过日记本，安静地翻了几页，时间悄然流逝至黄昏深处\n                   和络络聊天: 青空黎试探性地问：「这本是从哪天开始写的？都写些什么呀？」\n                   </roleplay_options>\n                 ')},{role:'assistant',message:n('\n                   [查看日记: 真是的, 就这么喜欢看吗(v〃ω〃)]\n                   <roleplay_options>\n                   阅读日记第一页：青空黎打开粉色的日记本，从第一页开始认真阅读络络的记录内容。\n                   问她封面上的兔子贴纸：青空黎好奇那枚蓝色兔子贴纸的来历，转头向络络询问。\n                   观察络络的小动作：青空黎不急着翻开日记，而是注意到络络表情里一丝期待与不安。\n                   和她闲聊天气：青空黎随口聊起傍晚风有点凉，试图舒缓紧张气氛。\n                   调侃她：“有哪页是‘不许看’的？”青空黎轻松地试图化解她的小慌乱。\n                   转身回教室：青空黎接过日记却并未立刻翻开，而是表示回教室再看，打算慢慢阅读。\n                   </roleplay_options>\n                 ')}],{refresh:'all'})});