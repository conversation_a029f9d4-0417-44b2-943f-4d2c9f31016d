/**
 * 世界书优化器事件处理模块
 * 包含所有事件处理函数和事件绑定逻辑
 */

import { TavernAPI, loadAllData } from './api';
import { appState, safeGetLorebookEntries, toggleMultiSelectMode, toggleSelectedItem } from './state';
import {
  hidePanel,
  renderContent,
  showModal,
  showPanel,
  showProgressToast,
  showSuccessTick,
  updateSelectionCount,
} from './ui';
import { debounce, errorCatched } from './utils';

// --- 全局变量 ---
let parentWin: any;
let $: any;

/**
 * 设置全局依赖
 */
export const setGlobalDependencies = (jquery: any, parentWindow: any) => {
  $ = jquery;
  parentWin = parentWindow;
};

// --- 辅助函数 ---

/**
 * 获取项目键
 */
const getItemKey = ($item: any): string | null => {
  const isGlobalLoreTab = appState.activeTab === 'global-lore';
  const isBookHeader = $item.hasClass('wio-book-group');

  if (isGlobalLoreTab && isBookHeader) {
    const isEditingEntries = $item.hasClass('editing-entries');
    if (isEditingEntries) return null;

    const bookName = $item.data('book-name');
    return bookName ? `book:${bookName}` : null;
  }

  const type = $item.data('type');
  const id = $item.data('id');
  const bookName = $item.data('book-name');

  if (type === 'lore' && id && bookName) {
    return `entry:${bookName}:${id}`;
  } else if (type === 'regex' && id) {
    const scope = appState.activeTab === 'global-regex' ? 'global' : 'character';
    return `regex:${scope}:${id}`;
  }

  return null;
};

/**
 * 检查项目是否可选择
 */
const canSelectItem = ($item: any): boolean => {
  const isGlobalLoreTab = appState.activeTab === 'global-lore';
  const isBookHeader = $item.hasClass('wio-book-group');

  if (isGlobalLoreTab && isBookHeader) {
    return !$item.hasClass('editing-entries');
  }

  return true;
};

// --- 核心事件处理函数 ---

/**
 * 处理头部点击事件
 */
const handleHeaderClick = errorCatched(async (event: any) => {
  const $target = $(event.target);
  const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');

  // 如果点击的是按钮等可交互控件，则不执行后续逻辑
  if ($target.closest('.wio-item-controls, .wio-rename-ui').length > 0) {
    return;
  }

  // 多选模式下的选择逻辑
  if (appState.multiSelectMode) {
    const itemKey = getItemKey($container);
    if (itemKey && canSelectItem($container)) {
      toggleSelectedItem(itemKey);
      $container.toggleClass('selected');
      updateSelectionCount();
    }
    return;
  }

  // 普通模式下的展开/折叠逻辑
  const $content = $container.find('.wio-collapsible-content').first();
  if ($content.length > 0) {
    const isCollapsed = $container.hasClass('collapsed');
    if (isCollapsed) {
      $content.slideDown(200);
      $container.removeClass('collapsed');
    } else {
      $content.slideUp(200);
      $container.addClass('collapsed');
    }
  }
}, 'handleHeaderClick');

/**
 * 处理状态切换事件
 */
const handleToggleState = errorCatched(async (event: any) => {
  event.stopPropagation();
  const $button = $(event.currentTarget);
  const $elementToSort = $button.closest('.wio-book-group, .wio-item-container');
  if ($elementToSort.hasClass('renaming')) return;

  const type = $elementToSort.data('type') || 'book';
  const id = $elementToSort.data('id');
  const bookName = $elementToSort.data('book-name');

  try {
    if (type === 'book') {
      // 切换世界书状态
      const book = appState.allLorebooks.find(b => b.name === bookName);
      if (book) {
        const newState = !book.enabled;
        book.enabled = newState;

        // 更新UI
        const $icon = $button.find('i');
        $icon.removeClass('fa-eye fa-eye-slash');
        $icon.addClass(newState ? 'fa-eye' : 'fa-eye-slash');
        $button.attr('title', newState ? '禁用' : '启用');

        showSuccessTick(`世界书已${newState ? '启用' : '禁用'}`);
      }
    } else if (type === 'lore') {
      // 切换条目状态
      const entries = safeGetLorebookEntries(bookName);
      const entry = entries.find(e => e.uid === id);
      if (entry) {
        const newState = !entry.enabled;
        entry.enabled = newState;

        // 更新到服务器
        await TavernAPI.setLorebookEntries(bookName, [{ uid: id, enabled: newState }]);

        // 更新UI
        const $icon = $button.find('i');
        $icon.removeClass('fa-eye fa-eye-slash');
        $icon.addClass(newState ? 'fa-eye' : 'fa-eye-slash');
        $button.attr('title', newState ? '禁用' : '启用');

        showSuccessTick(`条目已${newState ? '启用' : '禁用'}`);
      }
    }
  } catch (error) {
    console.error('[WorldInfoOptimizer] Error toggling state:', error);
    showModal({ type: 'alert', title: '错误', text: '状态切换失败，请重试。' });
  }
}, 'handleToggleState');

/**
 * 绑定所有事件处理器
 */
export const bindEventHandlers = (): void => {
  const parentDoc = parentWin.document;

  // 扩展菜单按钮点击事件
  $(parentDoc).on('click', `#world-info-optimizer-button`, async () => {
    const $panel = $(`#world-info-optimizer-panel`, parentDoc);
    if ($panel.is(':visible')) {
      hidePanel();
    } else {
      await showPanel(() => loadAllData(renderContent));
    }
  });

  // 面板关闭按钮
  $(parentDoc).on('click', '#wio-close-btn', () => {
    hidePanel();
  });

  // 搜索输入框事件
  const debouncedSearch = debounce(() => {
    renderContent();
  }, 300);

  $(parentDoc).on('input', `#wio-search-input`, debouncedSearch);
  $(parentDoc).on('click', '#wio-clear-search-btn', () => {
    $(`#wio-search-input`, parentDoc).val('').trigger('input');
  });

  // 搜索过滤器复选框
  $(parentDoc).on('change', '#wio-filter-book-name', (e: any) => {
    appState.searchFilters.bookName = e.target.checked;
    renderContent();
  });
  $(parentDoc).on('change', '#wio-filter-entry-name', (e: any) => {
    appState.searchFilters.entryName = e.target.checked;
    renderContent();
  });
  $(parentDoc).on('change', '#wio-filter-keywords', (e: any) => {
    appState.searchFilters.keywords = e.target.checked;
    renderContent();
  });
  $(parentDoc).on('change', '#wio-filter-content', (e: any) => {
    appState.searchFilters.content = e.target.checked;
    renderContent();
  });

  // 刷新按钮
  $(parentDoc).on('click', `#wio-refresh-btn`, () => {
    loadAllData(renderContent);
  });

  // 标签页切换
  $(parentDoc).on('click', `.wio-tab-btn`, (event: any) => {
    const $this = $(event.currentTarget);
    const tabId = $this.data('tab');

    $(`.wio-tab-btn`, parentDoc).removeClass('active');
    $this.addClass('active');

    appState.activeTab = tabId;
    renderContent();
  });

  // 多选模式切换
  $(parentDoc).on('click', '#wio-multi-select-toggle', () => {
    toggleMultiSelectMode();
    renderContent();
  });

  // 批量操作按钮
  $(parentDoc).on('click', '#wio-select-all-btn', handleSelectAll);
  $(parentDoc).on('click', '#wio-deselect-all-btn', handleDeselectAll);
  $(parentDoc).on('click', '#wio-batch-enable-btn', handleBatchEnable);
  $(parentDoc).on('click', '#wio-batch-disable-btn', handleBatchDisable);

  // 折叠按钮
  $(parentDoc).on('click', `#wio-collapse-all-btn`, handleCollapseAll);
  $(parentDoc).on('click', `#wio-collapse-current-btn`, handleCollapseCurrent);

  // 动态事件绑定
  $(parentDoc).on('click', '.wio-item-header', handleHeaderClick);
  $(parentDoc).on('click', '.wio-toggle-state', handleToggleState);
};

// --- 批量操作处理函数 ---

/**
 * 处理全选
 */
const handleSelectAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $visibleItems = $(
    `#world-info-optimizer-panel .wio-item-container:visible, #world-info-optimizer-panel .wio-book-group:visible`,
    parentDoc,
  );

  $visibleItems.each((_: any, element: any) => {
    const $item = $(element);
    const itemKey = getItemKey($item);
    if (itemKey && canSelectItem($item)) {
      appState.selectedItems.add(itemKey);
      $item.addClass('selected');
    }
  });

  updateSelectionCount();
  showSuccessTick('已全选可见项目');
}, 'handleSelectAll');

/**
 * 处理取消全选
 */
const handleDeselectAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  appState.selectedItems.clear();
  $(`#world-info-optimizer-panel .selected`, parentDoc).removeClass('selected');
  updateSelectionCount();
  showSuccessTick('已取消全选');
}, 'handleDeselectAll');

/**
 * 处理批量启用
 */
const handleBatchEnable = errorCatched(async () => {
  const selectedItems = Array.from(appState.selectedItems);
  if (selectedItems.length === 0) {
    await showModal({ type: 'alert', title: '提示', text: '请先选择要启用的项目。' });
    return;
  }

  const progressToast = showProgressToast('正在批量启用...');
  let successCount = 0;

  try {
    for (const itemKey of selectedItems) {
      if (itemKey.startsWith('book:')) {
        const bookName = itemKey.substring(5);
        const book = appState.allLorebooks.find(b => b.name === bookName);
        if (book) {
          book.enabled = true;
          successCount++;
        }
      } else if (itemKey.startsWith('entry:')) {
        const [bookName, entryUid] = itemKey.substring(6).split(':');
        const entries = safeGetLorebookEntries(bookName);
        const entry = entries.find(e => e.uid === entryUid);
        if (entry) {
          entry.enabled = true;
          await TavernAPI.setLorebookEntries(bookName, [{ uid: entryUid, enabled: true }]);
          successCount++;
        }
      }
    }

    progressToast.remove();
    showSuccessTick(`成功启用 ${successCount} 个项目`);
    appState.selectedItems.clear();
    renderContent();
  } catch (error) {
    progressToast.remove();
    console.error('[WorldInfoOptimizer] Error in batch enable:', error);
    showModal({ type: 'alert', title: '错误', text: '批量启用操作失败。' });
  }
}, 'handleBatchEnable');

/**
 * 处理批量禁用
 */
const handleBatchDisable = errorCatched(async () => {
  const selectedItems = Array.from(appState.selectedItems);
  if (selectedItems.length === 0) {
    await showModal({ type: 'alert', title: '提示', text: '请先选择要禁用的项目。' });
    return;
  }

  const progressToast = showProgressToast('正在批量禁用...');
  let successCount = 0;

  try {
    for (const itemKey of selectedItems) {
      if (itemKey.startsWith('book:')) {
        const bookName = itemKey.substring(5);
        const book = appState.allLorebooks.find(b => b.name === bookName);
        if (book) {
          book.enabled = false;
          successCount++;
        }
      } else if (itemKey.startsWith('entry:')) {
        const [bookName, entryUid] = itemKey.substring(6).split(':');
        const entries = safeGetLorebookEntries(bookName);
        const entry = entries.find(e => e.uid === entryUid);
        if (entry) {
          entry.enabled = false;
          await TavernAPI.setLorebookEntries(bookName, [{ uid: entryUid, enabled: false }]);
          successCount++;
        }
      }
    }

    progressToast.remove();
    showSuccessTick(`成功禁用 ${successCount} 个项目`);
    appState.selectedItems.clear();
    renderContent();
  } catch (error) {
    progressToast.remove();
    console.error('[WorldInfoOptimizer] Error in batch disable:', error);
    showModal({ type: 'alert', title: '错误', text: '批量禁用操作失败。' });
  }
}, 'handleBatchDisable');

/**
 * 处理全部折叠
 */
const handleCollapseAll = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const $allCollapsible = $(`#world-info-optimizer-panel .wio-collapsible-content`, parentDoc);

  $allCollapsible.slideUp(200);
  $allCollapsible.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
  showSuccessTick('已折叠所有项目');
}, 'handleCollapseAll');

/**
 * 处理当前标签页折叠
 */
const handleCollapseCurrent = errorCatched(async () => {
  const parentDoc = parentWin.document;
  const activeTab = appState.activeTab;
  const $currentTabContent = $(
    `#world-info-optimizer-panel .wio-tab-content[data-tab="${activeTab}"] .wio-collapsible-content`,
    parentDoc,
  );

  $currentTabContent.slideUp(200);
  $currentTabContent.closest('.wio-item-container, .wio-book-group').addClass('collapsed');
  showSuccessTick('已折叠当前标签页项目');
}, 'handleCollapseCurrent');
