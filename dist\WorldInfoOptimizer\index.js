var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ () => {
    throw new Error("Module parse failed: Identifier 'onReady' has already been declared (117:13)\nFile was processed with these loaders:\n * ./node_modules/.pnpm/ts-loader@9.5.4_typescript@_9428a39fa4868ceec0792d817e2a6d01/node_modules/ts-loader/index.js\nYou may need an additional loader to handle the result of these loaders.\n|      * 等待DOM和API就绪\n|      */\n>     function onReady(callback) {\n|         const domSelector = '#extensionsMenu';\n|         const maxRetries = 100;");
  }
};

var __webpack_exports__ = {};

__webpack_modules__["./src/WorldInfoOptimizer/index.ts"]();